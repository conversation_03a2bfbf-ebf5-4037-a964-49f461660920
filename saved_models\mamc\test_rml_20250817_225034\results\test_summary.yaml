dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 33000
inference_performance:
  avg_inference_time_ms: 0.03502680316115871
  max_inference_time_ms: 0.7371604442596436
  min_inference_time_ms: 0.03102049231529236
  std_inference_time_ms: 0.03169842115640883
model_complexity:
  macs: 490.112K
  macs_raw: 490112.0
  parameters: 28.171K
  params_raw: 28171.0
overall_metrics:
  accuracy: 60.472727272727276
  kappa: 0.5651999999999999
  macro_f1: 62.43020067132202
test_info:
  config_path: config.yaml
  model_path: saved_models/mamc/rml_20250817_204730/models/best_model.pth
  test_date: '2025-08-17 22:51:03'
