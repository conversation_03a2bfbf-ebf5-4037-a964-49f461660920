<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="tqdm" />
            <item index="2" class="java.lang.String" itemvalue="scipy" />
            <item index="3" class="java.lang.String" itemvalue="torch" />
            <item index="4" class="java.lang.String" itemvalue="numpy" />
            <item index="5" class="java.lang.String" itemvalue="mamba_ssm" />
            <item index="6" class="java.lang.String" itemvalue="seaborn" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>