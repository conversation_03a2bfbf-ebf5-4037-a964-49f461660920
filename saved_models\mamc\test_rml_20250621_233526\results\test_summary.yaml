dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 66000
inference_performance:
  avg_inference_time_ms: 0.03576327815200343
  max_inference_time_ms: 0.7673799991607666
  min_inference_time_ms: 0.030267983675003052
  std_inference_time_ms: 0.02187967505858393
model_complexity:
  macs: 490.112K
  macs_raw: 490112.0
  parameters: 28.171K
  params_raw: 28171.0
overall_metrics:
  accuracy: 58.84090909090909
  kappa: 0.54725
  macro_f1: 60.357075760716505
test_info:
  config_path: config.yaml
  model_path: saved_models/mamc/rml_20250609_173058/models/best_model.pth
  test_date: '2025-06-21 23:35:58'
