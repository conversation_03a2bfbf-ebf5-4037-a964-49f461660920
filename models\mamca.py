"""
MAMCA (Mamba-based Automatic Modulation Classification with Attention) 模型实现

这个模块实现了完整的MAMCA模型，结合了：
1. 去噪单元 (Denoising Unit)
2. Mamba状态空间模型 (Selective SSM)
3. 分类器

主要特点：
- 基于Mamba的状态空间模型
- 自适应去噪技术
- 端到端训练
- 支持多种数据集
"""

import torch
import torch.nn as nn
from functools import partial
from .denoising_unit import DenosingUnit, BasicBlock
from .selective_ssm import MixerModel, _init_weights

# 尝试导入Mamba配置
try:
    from mamba_ssm.models.config_mamba import MambaConfig
    from mamba_ssm.utils.generation import GenerationMixin
    MAMBA_AVAILABLE = True
except ImportError:
    print("Warning: mamba_ssm not available, using simplified configuration")
    MAMBA_AVAILABLE = False
    MambaConfig = None
    GenerationMixin = object


class SimplifiedMambaConfig:
    """
    简化的Mamba配置类，当mamba_ssm不可用时使用
    """
    
    def __init__(self, d_model=16, n_layer=1, ssm_cfg=None, rms_norm=True, 
                 residual_in_fp32=False, fused_add_norm=False):
        self.d_model = d_model
        self.n_layer = n_layer
        self.ssm_cfg = ssm_cfg if ssm_cfg is not None else {"d_state": 16, "d_conv": 4, "expand": 2}
        self.rms_norm = rms_norm
        self.residual_in_fp32 = residual_in_fp32
        self.fused_add_norm = fused_add_norm


class MAMCA(nn.Module, GenerationMixin if MAMBA_AVAILABLE else object):
    """
    MAMCA模型的完整实现
    
    Args:
        config: Mamba配置对象
        initializer_cfg (dict): 权重初始化配置
        device: 设备
        dtype: 数据类型
        length (int): 输入序列长度
        num_classes (int): 分类类别数
        in_channels (int): 输入通道数，默认为2 (I/Q)
        denoising_out_channels (int): 去噪单元输出通道数，默认为16
        denoising_num_blocks (int): 去噪单元块数，默认为2
        dropout_rate (float): Dropout比率，默认为0.15
    """
    
    def __init__(self, config=None, initializer_cfg=None, device=None, dtype=None,
                 length=128, num_claasses=11) -> None:
        super().__init__()
        
        # 处理配置
        if config is None:
            config = SimplifiedMambaConfig()
        self.config = config
        
        # 从配置中获取参数
        d_model = config.d_model
        n_layer = config.n_layer
        ssm_cfg = config.ssm_cfg
        rms_norm = config.rms_norm
        residual_in_fp32 = config.residual_in_fp32
        fused_add_norm = config.fused_add_norm
        factory_kwargs = {"device": device, "dtype": dtype}
        
        # 保存模型参数 - 严格按照原始MAMC
        self.length = length
        self.num_claasses = num_claasses
        self.d_model = d_model

        # 初始化 backbone，使用 MixerModel 来构建基础的模型 - 严格按照原始MAMC
        self.backbone = MixerModel(
            d_model=d_model,
            n_layer=n_layer,
            ssm_cfg=ssm_cfg,
            rms_norm=rms_norm,
            initializer_cfg=initializer_cfg,
            fused_add_norm=fused_add_norm,
            residual_in_fp32=residual_in_fp32,
            **factory_kwargs,
        )

        # 初始化 denosing 单元，使用 BasicBlock 作为基础模块 - 严格按照原始MAMC
        self.denosing = DenosingUnit(BasicBlock, 2, in_channel=2, out_channel=16)

        # 初始化 Dropout 层，丢弃率为 0.15 - 严格按照原始MAMC
        self.drop = nn.Dropout(0.15)

        # 定义全连接层，将模型的输出映射到类别数上 - 严格按照原始MAMC
        self.fc = nn.Sequential(self.drop, nn.Linear(int(self.config.d_model*length), num_claasses))
        
        # 应用权重初始化
        self.apply(
            partial(
                _init_weights,
                n_layer=n_layer,
                **(initializer_cfg if initializer_cfg is not None else {}),
            )
        )

    def forward(self, hidden_states, inference_params=None):
        """前向传播方法 - 严格按照原始MAMC"""
        hidden_states = self.denosing(hidden_states)  # 对输入的 hidden_states 进行去噪处理
        hidden_states = self.drop(hidden_states)  # 对处理后的 hidden_states 应用 Dropout 层
        hidden_states = self.backbone(hidden_states, inference_params=inference_params)  # 通过 backbone 模型处理隐藏状态
        hidden_states = hidden_states.view(hidden_states.size(0), -1)  # 将隐藏状态展平（合并维度）
        hidden_states = self.fc(hidden_states)  # 通过全连接层进行最后的映射，输出类别预测

        return hidden_states  # 返回模型的输出结果
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': 'MAMCA',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_shape': f'[batch_size, 2, {self.length}]',
            'output_shape': f'[batch_size, {self.num_claasses}]',
            'sequence_length': self.length,
            'num_classes': self.num_claasses,
            'd_model': self.d_model,
            'n_layer': self.config.n_layer,
            'ssm_cfg': self.config.ssm_cfg,
            'mamba_available': MAMBA_AVAILABLE
        }


def create_mamca_config(d_model=16, n_layer=1, d_state=16, d_conv=4, expand=2, 
                       rms_norm=True, residual_in_fp32=False, fused_add_norm=False):
    """
    创建MAMCA配置的便捷函数
    
    Args:
        d_model (int): 模型维度
        n_layer (int): 层数
        d_state (int): 状态维度
        d_conv (int): 卷积维度
        expand (int): 扩展因子
        rms_norm (bool): 是否使用RMSNorm
        residual_in_fp32 (bool): 是否在fp32中计算残差
        fused_add_norm (bool): 是否使用融合归一化
        
    Returns:
        配置对象
    """
    ssm_cfg = {"d_state": d_state, "d_conv": d_conv, "expand": expand}
    
    if MAMBA_AVAILABLE and MambaConfig is not None:
        config = MambaConfig()
        config.d_model = d_model
        config.n_layer = n_layer
        config.ssm_cfg = ssm_cfg
        config.rms_norm = rms_norm
        config.residual_in_fp32 = residual_in_fp32
        config.fused_add_norm = fused_add_norm
        return config
    else:
        return SimplifiedMambaConfig(
            d_model=d_model,
            n_layer=n_layer,
            ssm_cfg=ssm_cfg,
            rms_norm=rms_norm,
            residual_in_fp32=residual_in_fp32,
            fused_add_norm=fused_add_norm
        )


def create_mamca_for_dataset(dataset_type, num_classes, sequence_length):
    """
    为特定数据集创建MAMCA模型
    
    Args:
        dataset_type (str): 数据集类型
        num_classes (int): 类别数
        sequence_length (int): 序列长度
        
    Returns:
        MAMCA: 配置好的MAMCA模型
    """
    # 根据数据集调整参数
    if dataset_type == 'rml':
        # RML数据集参数
        config = create_mamca_config(d_model=16, n_layer=1, d_state=16, d_conv=4, expand=2)
        denoising_out_channels = 16
        dropout_rate = 0.15
    elif dataset_type == 'hisar':
        # HisarMod数据集参数
        config = create_mamca_config(d_model=16, n_layer=1, d_state=16, d_conv=4, expand=2)
        denoising_out_channels = 16
        dropout_rate = 0.15
    elif dataset_type.startswith('torchsig'):
        # TorchSig数据集参数
        config = create_mamca_config(d_model=20, n_layer=2, d_state=20, d_conv=4, expand=2)
        denoising_out_channels = 20
        dropout_rate = 0.1
    else:
        # 默认参数
        config = create_mamca_config(d_model=16, n_layer=1, d_state=16, d_conv=4, expand=2)
        denoising_out_channels = 16
        dropout_rate = 0.15
    
    return MAMCA(
        config=config,
        length=sequence_length,
        num_classes=num_classes,
        in_channels=2,
        denoising_out_channels=denoising_out_channels,
        dropout_rate=dropout_rate
    )


if __name__ == '__main__':
    # 测试MAMCA模型
    print("测试MAMCA模型...")
    print(f"Mamba可用性: {MAMBA_AVAILABLE}")
    
    # 创建配置
    config = create_mamca_config(d_model=16, n_layer=1)
    
    # 创建模型
    model = MAMCA(
        config=config,
        length=1024,
        num_classes=26,
        in_channels=2,
        denoising_out_channels=16
    )
    
    # 打印模型信息
    info = model.get_model_info()
    print("MAMCA模型信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 测试前向传播
    batch_size = 4
    test_input = torch.randn(batch_size, 2, 1024)
    
    print(f"\n测试输入形状: {test_input.shape}")
    
    with torch.no_grad():
        output = model(test_input)
        print(f"输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
    
    print("MAMCA模型测试完成！")
