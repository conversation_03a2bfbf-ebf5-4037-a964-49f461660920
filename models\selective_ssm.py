"""
MAMC选择性状态空间模型实现

这个模块实现了基于Mamba的选择性状态空间模型，包括：
1. Block - Mamba块
2. MixerModel - 完整的Mamba模型
3. 权重初始化函数

主要特点：
- 基于Mamba的状态空间模型
- 支持融合归一化
- 残差连接
- 可配置的层数和维度

注意：这个实现需要mamba_ssm库，如果没有安装会使用简化版本
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional
import math
from functools import partial

# 尝试导入Mamba相关模块
try:
    from mamba_ssm.modules.mamba_simple import Mamba
    from mamba_ssm.ops.triton.layernorm import RMSNorm, layer_norm_fn, rms_norm_fn
    MAMBA_AVAILABLE = True
except ImportError:
    print("Warning: mamba_ssm not available, using simplified implementation")
    MAMBA_AVAILABLE = False
    Mamba = None
    RMSNorm = None
    layer_norm_fn = None
    rms_norm_fn = None


class SimplifiedMamba(nn.Module):
    """
    简化的Mamba实现，当mamba_ssm不可用时使用
    
    这是一个基于LSTM的简化实现，保持接口兼容性
    """
    
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2, **kwargs):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        
        # 使用LSTM作为简化的状态空间模型
        self.lstm = nn.LSTM(
            input_size=d_model,
            hidden_size=d_model,
            num_layers=1,
            batch_first=True,
            bidirectional=False
        )
        
        # 线性投影层
        self.in_proj = nn.Linear(d_model, d_model * expand)
        self.out_proj = nn.Linear(d_model * expand, d_model)
        
        # 激活函数
        self.act = nn.SiLU()
        
    def forward(self, x, inference_params=None):
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            inference_params: 推理参数（兼容性参数）
            
        Returns:
            输出张量 [batch_size, seq_len, d_model]
        """
        batch_size, seq_len, d_model = x.shape
        
        # 输入投影
        x_proj = self.in_proj(x)
        x_proj = self.act(x_proj)
        
        # LSTM处理
        lstm_out, _ = self.lstm(x_proj)
        
        # 输出投影
        output = self.out_proj(lstm_out)
        
        return output


class Block(nn.Module):
    """
    Mamba块
    
    包含Mamba mixer和归一化层的基础块。
    
    Args:
        dim (int): 模型维度
        mixer_cls: Mixer类（Mamba或SimplifiedMamba）
        norm_cls: 归一化类
        fused_add_norm (bool): 是否使用融合归一化
        residual_in_fp32 (bool): 是否在fp32中计算残差
    """
    
    def __init__(self, dim, mixer_cls, norm_cls=nn.LayerNorm, fused_add_norm=False, residual_in_fp32=False):
        super().__init__()
        self.residual_in_fp32 = residual_in_fp32
        self.fused_add_norm = fused_add_norm
        self.mixer = mixer_cls(dim)
        self.norm = norm_cls(dim)

        if self.fused_add_norm:
            if not MAMBA_AVAILABLE:
                print("Warning: Fused add norm not available without mamba_ssm, using standard norm")
                self.fused_add_norm = False
            else:
                assert RMSNorm is not None, "RMSNorm import fails"
                assert isinstance(self.norm, (nn.LayerNorm, RMSNorm)), \
                    "Only LayerNorm and RMSNorm are supported for fused_add_norm"

    def forward(self, hidden_states: Tensor, residual: Optional[Tensor] = None, inference_params=None):
        """
        前向传播
        
        Args:
            hidden_states: 隐藏状态
            residual: 残差连接
            inference_params: 推理参数
            
        Returns:
            (hidden_states, residual): 处理后的隐藏状态和残差
        """
        if not self.fused_add_norm:
            # 标准归一化
            residual = (hidden_states + residual) if residual is not None else hidden_states
            hidden_states = self.norm(residual.to(dtype=self.norm.weight.dtype))
            if self.residual_in_fp32:
                residual = residual.to(torch.float32)
        else:
            # 融合归一化（仅在mamba_ssm可用时）
            fused_add_norm_fn = rms_norm_fn if isinstance(self.norm, RMSNorm) else layer_norm_fn
            hidden_states, residual = fused_add_norm_fn(
                hidden_states,
                self.norm.weight,
                self.norm.bias,
                residual=residual,
                prenorm=True,
                residual_in_fp32=self.residual_in_fp32,
                eps=self.norm.eps,
            )
        
        # 通过mixer处理
        hidden_states = self.mixer(hidden_states, inference_params=inference_params)
        return hidden_states, residual


def create_block(d_model, ssm_cfg=None, norm_epsilon=1e-5, rms_norm=True, 
                residual_in_fp32=False, fused_add_norm=False, layer_idx=None, 
                device=None, dtype=None):
    """
    创建Mamba块
    
    Args:
        d_model (int): 模型维度
        ssm_cfg (dict): SSM配置
        norm_epsilon (float): 归一化epsilon
        rms_norm (bool): 是否使用RMSNorm
        residual_in_fp32 (bool): 是否在fp32中计算残差
        fused_add_norm (bool): 是否使用融合归一化
        layer_idx (int): 层索引
        device: 设备
        dtype: 数据类型
        
    Returns:
        Block: 配置好的Mamba块
    """
    if ssm_cfg is None:
        ssm_cfg = {}
    
    factory_kwargs = {"device": device, "dtype": dtype}
    
    # 选择Mixer类
    if MAMBA_AVAILABLE and Mamba is not None:
        mixer_cls = partial(Mamba, layer_idx=layer_idx, **ssm_cfg, **factory_kwargs)
    else:
        mixer_cls = partial(SimplifiedMamba, **ssm_cfg, **factory_kwargs)
    
    # 选择归一化类
    if rms_norm and MAMBA_AVAILABLE and RMSNorm is not None:
        norm_cls = partial(RMSNorm, eps=norm_epsilon, **factory_kwargs)
    else:
        norm_cls = partial(nn.LayerNorm, eps=norm_epsilon, **factory_kwargs)
    
    # 创建块
    block = Block(
        d_model,
        mixer_cls,
        norm_cls=norm_cls,
        fused_add_norm=fused_add_norm,
        residual_in_fp32=residual_in_fp32,
    )
    block.layer_idx = layer_idx
    return block


def _init_weights(module, n_layer, initializer_range=0.02, rescale_prenorm_residual=True, n_residuals_per_layer=1):
    """
    权重初始化函数
    
    Args:
        module: 要初始化的模块
        n_layer (int): 层数
        initializer_range (float): 初始化范围
        rescale_prenorm_residual (bool): 是否重新缩放预归一化残差
        n_residuals_per_layer (int): 每层残差数
    """
    if isinstance(module, nn.Linear):
        if module.bias is not None:
            if not getattr(module.bias, "_no_reinit", False):
                nn.init.zeros_(module.bias)
    elif isinstance(module, nn.Embedding):
        nn.init.normal_(module.weight, std=initializer_range)

    if rescale_prenorm_residual:
        for name, p in module.named_parameters():
            if name in ["out_proj.weight", "fc2.weight"]:
                nn.init.kaiming_uniform_(p, a=math.sqrt(5))
                with torch.no_grad():
                    p /= math.sqrt(n_residuals_per_layer * n_layer)


class MixerModel(nn.Module):
    """
    完整的Mamba Mixer模型
    
    包含多个Mamba块的完整模型。
    
    Args:
        d_model (int): 模型维度
        n_layer (int): 层数
        ssm_cfg (dict): SSM配置
        norm_epsilon (float): 归一化epsilon
        rms_norm (bool): 是否使用RMSNorm
        initializer_cfg (dict): 初始化配置
        fused_add_norm (bool): 是否使用融合归一化
        residual_in_fp32 (bool): 是否在fp32中计算残差
        device: 设备
        dtype: 数据类型
    """
    
    def __init__(self, d_model: int, n_layer: int, ssm_cfg=None, norm_epsilon: float = 1e-5,
                 rms_norm: bool = True, initializer_cfg=None, fused_add_norm=False,
                 residual_in_fp32=False, device=None, dtype=None) -> None:
        factory_kwargs = {"device": device, "dtype": dtype}
        super().__init__()
        self.residual_in_fp32 = residual_in_fp32
        self.fused_add_norm = fused_add_norm
        
        if self.fused_add_norm and not MAMBA_AVAILABLE:
            print("Warning: Fused add norm not available without mamba_ssm")
            self.fused_add_norm = False

        # 创建多个Mamba块
        self.layers = nn.ModuleList([
            create_block(
                d_model,
                ssm_cfg=ssm_cfg,
                norm_epsilon=norm_epsilon,
                rms_norm=rms_norm,
                residual_in_fp32=residual_in_fp32,
                fused_add_norm=fused_add_norm,
                layer_idx=i,
                **factory_kwargs,
            )
            for i in range(n_layer)
        ])

        # 最终归一化层
        if rms_norm and MAMBA_AVAILABLE and RMSNorm is not None:
            self.norm_f = RMSNorm(d_model, eps=norm_epsilon, **factory_kwargs)
        else:
            self.norm_f = nn.LayerNorm(d_model, eps=norm_epsilon, **factory_kwargs)

        # 应用权重初始化
        self.apply(
            partial(
                _init_weights,
                n_layer=n_layer,
                **(initializer_cfg if initializer_cfg is not None else {}),
            )
        )

    def allocate_inference_cache(self, batch_size, max_seqlen, dtype=None, **kwargs):
        """分配推理缓存"""
        return {
            i: layer.allocate_inference_cache(batch_size, max_seqlen, dtype=dtype, **kwargs)
            for i, layer in enumerate(self.layers)
        }

    def forward(self, input_ids, inference_params=None):
        """
        前向传播
        
        Args:
            input_ids: 输入张量 [batch_size, channels, seq_len]
            inference_params: 推理参数
            
        Returns:
            处理后的隐藏状态 [batch_size, seq_len, d_model]
        """
        hidden_states = input_ids
        # 转置：[batch_size, channels, seq_len] -> [batch_size, seq_len, channels]
        hidden_states = torch.permute(hidden_states, (0, 2, 1))
        residual = None
        
        # 通过所有层
        for layer in self.layers:
            hidden_states, residual = layer(
                hidden_states, residual, inference_params=inference_params
            )
        
        # 最终归一化
        if not self.fused_add_norm:
            residual = (hidden_states + residual) if residual is not None else hidden_states
            hidden_states = self.norm_f(residual.to(dtype=self.norm_f.weight.dtype))
        else:
            # 融合归一化（仅在mamba_ssm可用时）
            if MAMBA_AVAILABLE:
                fused_add_norm_fn = rms_norm_fn if isinstance(self.norm_f, RMSNorm) else layer_norm_fn
                hidden_states = fused_add_norm_fn(
                    hidden_states,
                    self.norm_f.weight,
                    self.norm_f.bias,
                    eps=self.norm_f.eps,
                    residual=residual,
                    prenorm=False,
                    residual_in_fp32=self.residual_in_fp32,
                )
            else:
                # 回退到标准归一化
                residual = (hidden_states + residual) if residual is not None else hidden_states
                hidden_states = self.norm_f(residual.to(dtype=self.norm_f.weight.dtype))

        return hidden_states


if __name__ == '__main__':
    # 测试选择性SSM
    print("测试MAMC选择性状态空间模型...")
    print(f"Mamba可用性: {MAMBA_AVAILABLE}")
    
    # 创建模型
    d_model = 16
    n_layer = 1
    ssm_cfg = {"d_state": 16, "d_conv": 4, "expand": 2}
    
    model = MixerModel(
        d_model=d_model,
        n_layer=n_layer,
        ssm_cfg=ssm_cfg,
        rms_norm=True,
        fused_add_norm=False,  # 在测试中禁用融合归一化
        residual_in_fp32=False
    )
    
    # 测试输入
    batch_size = 4
    channels = 16
    seq_len = 1024
    test_input = torch.randn(batch_size, channels, seq_len)
    
    print(f"输入形状: {test_input.shape}")
    
    # 前向传播
    with torch.no_grad():
        output = model(test_input)
        print(f"输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数数: {total_params}")
    print(f"可训练参数数: {trainable_params}")
    
    print("选择性SSM测试完成！")
